import mongoose from "mongoose";
import dotenv from "dotenv";

// Import models
import Tooling from "../models/tooling.modal.js";
import Responsible from "../models/responsible.modal.js";
import Location from "../models/location.modal.js";
import Placement from "../models/placement.modal.js";

// Load environment variables
dotenv.config();

/**
 * Convert string values to ObjectId references in existing Tooling documents
 */
async function convertStringToObjectId() {
  const startTime = Date.now();
  
  try {
    console.log("🚀 Starting string to ObjectId conversion...");
    console.log(`⏰ Start time: ${new Date().toISOString()}`);

    // Validate environment
    if (!process.env.MONGO_URI) {
      throw new Error("MONGO_URI environment variable is not set");
    }

    // Connect to MongoDB
    console.log("🔌 Connecting to MongoDB...");
    await mongoose.connect(process.env.MONGO_URI, {
      serverSelectionTimeoutMS: 5000,
    });
    console.log("✅ Connected to MongoDB");

    // Find all tools with string references
    console.log("🔍 Finding tools with string references...");
    const toolsWithStrings = await Tooling.find({
      $or: [
        { responsible: { $type: "string" } },
        { location: { $type: "string" } },
        { placement: { $type: "string" } }
      ]
    });

    console.log(`📊 Found ${toolsWithStrings.length} tools with string references`);

    if (toolsWithStrings.length === 0) {
      console.log("✅ No tools need conversion. All references are already ObjectIds.");
      return { success: true, converted: 0 };
    }

    // Collect unique string values
    const uniqueResponsibles = new Set();
    const uniqueLocations = new Set();
    const uniquePlacements = new Set();

    toolsWithStrings.forEach(tool => {
      if (typeof tool.responsible === 'string' && tool.responsible) {
        uniqueResponsibles.add(tool.responsible);
      }
      if (typeof tool.location === 'string' && tool.location) {
        uniqueLocations.add(tool.location);
      }
      if (typeof tool.placement === 'string' && tool.placement) {
        uniquePlacements.add(tool.placement);
      }
    });

    console.log(`📋 Unique responsibles: ${uniqueResponsibles.size}`);
    console.log(`📋 Unique locations: ${uniqueLocations.size}`);
    console.log(`📋 Unique placements: ${uniquePlacements.size}`);

    // Create or find responsible documents
    const responsibleMap = new Map();
    for (const responsibleName of uniqueResponsibles) {
      try {
        let responsible = await Responsible.findOne({ 
          name: new RegExp(`^${responsibleName}$`, "i") 
        });

        if (!responsible) {
          // Extract grade from name (assuming format like "Cne Rami Dalli")
          const nameParts = responsibleName.split(' ');
          const grade = nameParts[0]; // First part is usually the grade
          const name = responsibleName;

          responsible = new Responsible({ name, grade });
          await responsible.save();
          console.log(`✅ Created responsible: ${responsibleName}`);
        }
        
        responsibleMap.set(responsibleName, responsible._id);
      } catch (error) {
        console.error(`❌ Error creating responsible ${responsibleName}:`, error.message);
      }
    }

    // Create or find location documents
    const locationMap = new Map();
    for (const locationName of uniqueLocations) {
      try {
        let location = await Location.findOne({ 
          name: new RegExp(`^${locationName}$`, "i") 
        });

        if (!location) {
          location = new Location({ 
            name: locationName,
            description: "Auto-created during string to ObjectId conversion"
          });
          await location.save();
          console.log(`✅ Created location: ${locationName}`);
        }
        
        locationMap.set(locationName, location._id);
      } catch (error) {
        console.error(`❌ Error creating location ${locationName}:`, error.message);
      }
    }

    // Create or find placement documents
    const placementMap = new Map();
    for (const placementName of uniquePlacements) {
      try {
        let placement = await Placement.findOne({ 
          name: new RegExp(`^${placementName}$`, "i") 
        });

        if (!placement) {
          placement = new Placement({ 
            name: placementName,
            description: "Auto-created during string to ObjectId conversion"
          });
          await placement.save();
          console.log(`✅ Created placement: ${placementName}`);
        }
        
        placementMap.set(placementName, placement._id);
      } catch (error) {
        console.error(`❌ Error creating placement ${placementName}:`, error.message);
      }
    }

    // Convert tools
    console.log("\n🔄 Converting tool references...");
    let convertedCount = 0;
    let errorCount = 0;

    for (const tool of toolsWithStrings) {
      try {
        let updated = false;

        // Convert responsible
        if (typeof tool.responsible === 'string' && tool.responsible) {
          const objectId = responsibleMap.get(tool.responsible);
          if (objectId) {
            tool.responsible = objectId;
            updated = true;
          }
        }

        // Convert location
        if (typeof tool.location === 'string' && tool.location) {
          const objectId = locationMap.get(tool.location);
          if (objectId) {
            tool.location = objectId;
            updated = true;
          }
        }

        // Convert placement
        if (typeof tool.placement === 'string' && tool.placement) {
          const objectId = placementMap.get(tool.placement);
          if (objectId) {
            tool.placement = objectId;
            updated = true;
          }
        }

        if (updated) {
          await tool.save();
          console.log(`✅ Converted: ${tool.designation} (MAT: ${tool.mat})`);
          convertedCount++;
        }

      } catch (error) {
        console.error(`❌ Error converting ${tool.designation}:`, error.message);
        errorCount++;
      }
    }

    // Calculate execution time
    const executionTime = Date.now() - startTime;
    
    // Print summary
    console.log("\n" + "=".repeat(50));
    console.log("📊 CONVERSION SUMMARY");
    console.log("=".repeat(50));
    console.log(`✅ Successfully converted: ${convertedCount} tools`);
    console.log(`❌ Errors: ${errorCount} tools`);
    console.log(`👥 Responsibles processed: ${uniqueResponsibles.size}`);
    console.log(`📍 Locations processed: ${uniqueLocations.size}`);
    console.log(`🏢 Placements processed: ${uniquePlacements.size}`);
    console.log(`⏱️ Total execution time: ${(executionTime / 1000).toFixed(2)} seconds`);
    console.log(`📅 Completed at: ${new Date().toISOString()}`);

    console.log("\n🎉 Conversion process completed successfully!");
    
    return {
      success: true,
      converted: convertedCount,
      errors: errorCount,
      executionTime: executionTime
    };

  } catch (error) {
    const executionTime = Date.now() - startTime;
    
    console.error("\n" + "=".repeat(50));
    console.error("💥 CONVERSION FAILED");
    console.error("=".repeat(50));
    console.error(`❌ Error: ${error.message}`);
    console.error(`⏱️ Failed after: ${(executionTime / 1000).toFixed(2)} seconds`);
    console.error(`📅 Failed at: ${new Date().toISOString()}`);
    
    return {
      success: false,
      error: error.message,
      executionTime: executionTime
    };
    
  } finally {
    // Close MongoDB connection
    try {
      await mongoose.disconnect();
      console.log("🔌 Disconnected from MongoDB");
    } catch (disconnectError) {
      console.error("⚠️ Error disconnecting from MongoDB:", disconnectError.message);
    }
  }
}

// Run the conversion if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  convertStringToObjectId()
    .then((result) => {
      if (result.success) {
        console.log("\n✅ Conversion completed successfully!");
        process.exit(0);
      } else {
        console.log("\n❌ Conversion failed!");
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error("\n💥 Unexpected error:", error.message);
      process.exit(1);
    });
}

export default convertStringToObjectId;
