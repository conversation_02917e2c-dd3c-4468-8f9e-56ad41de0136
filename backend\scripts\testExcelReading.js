import ExcelJS from "exceljs";
import path from "path";
import { fileURLToPath } from "url";
import {
  mapToolType,
  parseExcelDate,
  validateDirection,
} from "./importHelpers.js";

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Test function to validate Excel file reading without database connection
 */
async function testExcelReading() {
  try {
    console.log("🧪 Testing Excel file reading...");
    console.log("=" .repeat(50));

    // Path to the Excel file
    const excelFilePath = path.join(__dirname, "../../frontend/src/data/liste des équipement.xlsx");
    console.log(`📁 Reading Excel file: ${excelFilePath}`);

    // Validate file exists
    try {
      await import('fs').then(fs => fs.promises.access(excelFilePath));
      console.log("✅ Excel file found");
    } catch (error) {
      throw new Error(`Excel file not found: ${excelFilePath}`);
    }

    // Create workbook and read the Excel file
    const workbook = new ExcelJS.Workbook();
    try {
      await workbook.xlsx.readFile(excelFilePath);
      console.log("✅ Excel file read successfully");
    } catch (error) {
      throw new Error(`Failed to read Excel file: ${error.message}`);
    }

    // Get the first worksheet
    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      throw new Error("No worksheet found in the Excel file");
    }

    console.log(`📊 Worksheet: ${worksheet.name}`);
    console.log(`📋 Total rows: ${worksheet.rowCount}`);

    // Get header row to understand column structure
    const headerRow = worksheet.getRow(1);
    const headers = [];
    headerRow.eachCell((cell, colNumber) => {
      headers[colNumber] = cell.value?.toString().toLowerCase().trim();
    });

    console.log("\n📝 Headers found:");
    headers.forEach((header, index) => {
      if (header) {
        console.log(`   Column ${index}: "${header}"`);
      }
    });

    // Define column mapping based on expected headers
    const columnMapping = {
      designation: findColumnIndex(headers, ["designation", "désignation"]),
      type: findColumnIndex(headers, ["type"]),
      emplacement: findColumnIndex(headers, ["emplacement", "placement"]),
      location: findColumnIndex(headers, ["location", "lieu"]),
      reference: findColumnIndex(headers, ["reference", "référence"]),
      dateReference: findColumnIndex(headers, ["datereference", "date", "date référence"]),
      responsible: findColumnIndex(headers, ["responsible", "responsable"]),
      direction: findColumnIndex(headers, ["direction"]),
    };

    console.log("\n🗺️ Column mapping:");
    Object.entries(columnMapping).forEach(([field, columnIndex]) => {
      const status = columnIndex ? "✅ Found" : "❌ Not found";
      const header = columnIndex ? headers[columnIndex] : "N/A";
      console.log(`   ${field}: ${status} (Column ${columnIndex}, Header: "${header}")`);
    });

    // Validate that required columns are found
    const requiredColumns = ["designation", "type", "direction"];
    const missingColumns = [];
    for (const col of requiredColumns) {
      if (!columnMapping[col]) {
        missingColumns.push(col);
      }
    }

    if (missingColumns.length > 0) {
      throw new Error(`Required columns not found: ${missingColumns.join(", ")}`);
    }

    console.log("\n✅ All required columns found");

    // Test reading first few data rows
    console.log("\n📋 Sample data (first 5 rows):");
    console.log("-".repeat(80));

    for (let rowNumber = 2; rowNumber <= Math.min(7, worksheet.rowCount); rowNumber++) {
      const row = worksheet.getRow(rowNumber);
      
      // Extract data from row
      const rowData = extractRowData(row, columnMapping);
      
      // Skip empty rows
      if (!rowData.designation || rowData.designation.trim() === "") {
        console.log(`Row ${rowNumber}: [EMPTY - SKIPPED]`);
        continue;
      }

      console.log(`\nRow ${rowNumber}:`);
      console.log(`   Designation: "${rowData.designation}"`);
      console.log(`   Type: "${rowData.type}" → "${mapToolType(rowData.type)}"`);
      console.log(`   Emplacement: "${rowData.emplacement}"`);
      console.log(`   Location: "${rowData.location}"`);
      console.log(`   Reference: "${rowData.reference}"`);
      console.log(`   Date: "${rowData.dateReference}" → ${parseExcelDate(rowData.dateReference)}`);
      console.log(`   Responsible: "${rowData.responsible}"`);
      console.log(`   Direction: "${rowData.direction}" → "${validateDirection(rowData.direction)}"`);
    }

    // Count total non-empty rows
    let totalDataRows = 0;
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);
      const designation = getCellValue(row, columnMapping.designation);
      if (designation && designation.trim() !== "") {
        totalDataRows++;
      }
    }

    console.log("\n📊 Summary:");
    console.log(`   Total rows in Excel: ${worksheet.rowCount}`);
    console.log(`   Header row: 1`);
    console.log(`   Data rows: ${totalDataRows}`);
    console.log(`   Empty rows: ${worksheet.rowCount - 1 - totalDataRows}`);

    console.log("\n🎉 Excel file validation completed successfully!");
    console.log("✅ The file is ready for import when database is available");

    return {
      success: true,
      totalRows: worksheet.rowCount,
      dataRows: totalDataRows,
      columnMapping: columnMapping
    };

  } catch (error) {
    console.error("\n💥 Excel validation failed:", error.message);
    if (error.stack) {
      console.error("\n🔍 Stack trace:");
      console.error(error.stack);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Helper function to find column index by header names
 */
function findColumnIndex(headers, possibleNames) {
  for (const name of possibleNames) {
    const index = headers.findIndex(header => 
      header && header.includes(name.toLowerCase())
    );
    if (index !== -1) return index;
  }
  return null;
}

/**
 * Helper function to extract data from a row
 */
function extractRowData(row, columnMapping) {
  return {
    designation: getCellValue(row, columnMapping.designation),
    type: getCellValue(row, columnMapping.type),
    emplacement: getCellValue(row, columnMapping.emplacement),
    location: getCellValue(row, columnMapping.location),
    reference: getCellValue(row, columnMapping.reference),
    dateReference: getCellValue(row, columnMapping.dateReference),
    responsible: getCellValue(row, columnMapping.responsible),
    direction: getCellValue(row, columnMapping.direction),
  };
}

/**
 * Helper function to get cell value safely
 */
function getCellValue(row, columnIndex) {
  if (!columnIndex) return null;
  const cell = row.getCell(columnIndex);
  return cell.value?.toString().trim() || null;
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testExcelReading()
    .then((result) => {
      if (result.success) {
        console.log("\n✅ Test completed successfully!");
        process.exit(0);
      } else {
        console.log("\n❌ Test failed!");
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error("\n💥 Unexpected error:", error.message);
      process.exit(1);
    });
}

export default testExcelReading;
