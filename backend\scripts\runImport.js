#!/usr/bin/env node

/**
 * Simple runner script for the Excel import
 * Usage: node backend/scripts/runImport.js
 */

import importExcelData from './importExcel.js';

console.log("🚀 Starting Excel Import Runner...");
console.log("=" .repeat(50));

// Run the import
importExcelData()
  .then((result) => {
    if (result.success) {
      console.log("\n✅ Import completed successfully!");
      console.log(`📊 Summary: ${result.summary.success} imported, ${result.summary.skipped} skipped, ${result.summary.errors} errors`);
      process.exit(0);
    } else {
      console.log("\n❌ Import failed!");
      console.log(`💥 Error: ${result.error}`);
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error("\n💥 Unexpected error:", error.message);
    console.error(error.stack);
    process.exit(1);
  });
