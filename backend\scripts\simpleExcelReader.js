import ExcelJS from "exceljs";
import fs from "fs";

console.log("🚀 Starting simple Excel reader...");

try {
  // Read the Excel file
  const workbook = new ExcelJS.Workbook();
  const filePath = "frontend/src/data/liste des équipement.xlsx";
  
  console.log("📁 Reading file:", filePath);
  
  await workbook.xlsx.readFile(filePath);
  console.log("✅ File read successfully");
  
  // Get first worksheet
  const worksheet = workbook.getWorksheet(1);
  console.log("📊 Worksheet name:", worksheet.name);
  console.log("📋 Total rows:", worksheet.rowCount);
  
  // Read headers
  const headerRow = worksheet.getRow(1);
  console.log("\n📝 Headers:");
  headerRow.eachCell((cell, colNumber) => {
    console.log(`  Column ${colNumber}: "${cell.value}"`);
  });
  
  // Read first few data rows
  console.log("\n📋 Sample data:");
  for (let i = 2; i <= Math.min(5, worksheet.rowCount); i++) {
    const row = worksheet.getRow(i);
    console.log(`\nRow ${i}:`);
    row.eachCell((cell, colNumber) => {
      if (cell.value) {
        console.log(`  Col ${colNumber}: "${cell.value}"`);
      }
    });
  }
  
  console.log("\n🎉 Excel reading completed!");
  
} catch (error) {
  console.error("❌ Error:", error.message);
  console.error(error.stack);
}
