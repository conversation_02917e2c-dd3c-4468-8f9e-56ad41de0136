import ExcelJS from "exceljs";
import fs from "fs";

console.log("🚀 Generating complete JSON from Excel...");

try {
  // Read the Excel file
  const workbook = new ExcelJS.Workbook();
  const filePath = "frontend/src/data/liste des équipement.xlsx";
  
  console.log("📁 Reading file:", filePath);
  await workbook.xlsx.readFile(filePath);
  
  const worksheet = workbook.getWorksheet(1);
  console.log("📊 Processing", worksheet.rowCount, "rows");
  
  // Column mapping based on the actual structure (Row 4 has headers)
  const columnMap = {
    ordre: 1,        // "ordre"
    designation: 2,  // "désignation" 
    reference: 3,    // "PV / M11"
    date: 4,         // "Date"
    quantity: 5,     // "Quantité"
    location: 6,     // "Local"
    responsible: 7,  // "Responsible"
    placement: 8,    // "Emplacement"
    direction: 9,    // "Direction"
    type: 10         // "Type"
  };
  
  const tools = [];
  const responsibles = new Set();
  const locations = new Set();
  const placements = new Set();
  
  // Process data rows (starting from row 5)
  for (let rowNum = 5; rowNum <= worksheet.rowCount; rowNum++) {
    const row = worksheet.getRow(rowNum);
    
    // Get cell values
    const designation = getCellValue(row, columnMap.designation);
    const reference = getCellValue(row, columnMap.reference);
    const dateValue = getCellValue(row, columnMap.date);
    const quantity = getCellValue(row, columnMap.quantity);
    const location = getCellValue(row, columnMap.location);
    const responsible = getCellValue(row, columnMap.responsible);
    const placement = getCellValue(row, columnMap.placement);
    const direction = getCellValue(row, columnMap.direction);
    const type = getCellValue(row, columnMap.type);
    
    // Skip empty rows
    if (!designation || designation.trim() === "") {
      continue;
    }
    
    // Clean and process data
    const cleanDesignation = designation.toUpperCase().trim();
    const cleanType = mapType(type);
    const cleanDirection = mapDirection(direction);
    const acquisitionDate = parseDate(dateValue);
    const qty = parseInt(quantity) || 1;
    
    // Generate MAT code
    const prefix = cleanDesignation.substring(0, 2).toUpperCase();
    const existingWithPrefix = tools.filter(tool => tool.mat.startsWith(prefix));
    const matNumber = existingWithPrefix.length + 1;
    const mat = `${prefix}${String(matNumber).padStart(3, "0")}`;
    
    // Collect unique entities
    if (responsible) responsibles.add(responsible.trim());
    if (location) locations.add(location.trim());
    if (placement) placements.add(placement.trim());
    
    // Create tool object
    const tool = {
      designation: cleanDesignation,
      mat: mat,
      acquisitionType: "M11",
      acquisitionRef: reference || "inventaire 2025",
      acquisitionDate: acquisitionDate.toISOString(),
      originalQte: qty,
      currentQte: qty,
      responsible: responsible ? responsible.trim() : null,
      location: location ? location.trim() : null,
      placement: placement ? placement.trim() : null,
      type: cleanType,
      direction: cleanDirection,
      situation: "available",
      notes: `Imported from Excel on ${new Date().toISOString()}`,
      history: [
        {
          eventType: "entry",
          reference: `m11-${reference || "inventaire 2025"}`,
          date: acquisitionDate.toISOString(),
          qteChange: qty,
          notes: "Initial M11 acquisition from Excel import",
          performedBy: "system"
        }
      ],
      exits: []
    };
    
    tools.push(tool);
    
    if (tools.length % 50 === 0) {
      console.log(`✅ Processed ${tools.length} tools...`);
    }
  }
  
  // Create output JSON
  const output = {
    metadata: {
      exportDate: new Date().toISOString(),
      totalTools: tools.length,
      excelFile: "liste des équipement.xlsx",
      totalRows: worksheet.rowCount,
      dataStartRow: 5,
      instructions: "This JSON contains all tool data from Excel ready for MongoDB import"
    },
    relatedEntities: {
      responsibles: Array.from(responsibles).map(name => ({
        name,
        grade: extractGrade(name)
      })),
      locations: Array.from(locations).map(name => ({
        name,
        description: "Auto-created during Excel import"
      })),
      placements: Array.from(placements).map(name => ({
        name,
        description: "Auto-created during Excel import"
      }))
    },
    tools: tools
  };
  
  // Write to JSON file
  const outputPath = "frontend/src/data/tools_import.json";
  await fs.promises.writeFile(outputPath, JSON.stringify(output, null, 2), 'utf8');
  
  console.log("\n📊 Conversion Summary:");
  console.log(`✅ Total tools processed: ${tools.length}`);
  console.log(`👥 Unique responsibles: ${responsibles.size}`);
  console.log(`📍 Unique locations: ${locations.size}`);
  console.log(`🏢 Unique placements: ${placements.size}`);
  console.log(`💾 JSON saved to: ${outputPath}`);
  console.log("\n🎉 Complete JSON generation finished!");
  
} catch (error) {
  console.error("❌ Error:", error.message);
  console.error(error.stack);
}

function getCellValue(row, colIndex) {
  const cell = row.getCell(colIndex);
  return cell.value ? cell.value.toString().trim() : null;
}

function mapType(type) {
  if (!type) return "common";
  const t = type.toLowerCase();
  if (t.includes("maint")) return "maintenance";
  if (t.includes("cal")) return "calibration";
  if (t.includes("did")) return "didactic";
  return "common";
}

function mapDirection(direction) {
  if (!direction) return "DGMRE";
  const validDirections = ["DGMRE", "DGTI", "DGGM", "DHS", "DASIC"];
  const dir = direction.toUpperCase().trim();
  return validDirections.includes(dir) ? dir : "DGMRE";
}

function parseDate(dateValue) {
  if (!dateValue) return new Date();
  
  try {
    if (dateValue instanceof Date) return dateValue;
    if (typeof dateValue === "string") {
      // Handle the GMT format from Excel
      if (dateValue.includes("GMT")) {
        return new Date(dateValue);
      }
      return new Date(dateValue);
    }
    return new Date();
  } catch {
    return new Date();
  }
}

function extractGrade(name) {
  const militaryRanks = ["Gal", "Col", "Lcl", "Cdt", "Cne", "Lt", "Slt", "Adj", "Sgt", "Cpl", "Sol", "LT"];
  for (const rank of militaryRanks) {
    if (name.toLowerCase().startsWith(rank.toLowerCase())) {
      return rank;
    }
  }
  return "Unknown";
}
