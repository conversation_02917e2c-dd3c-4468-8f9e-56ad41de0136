import mongoose from "mongoose";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import dotenv from "dotenv";

// Import models
import Tooling from "../models/tooling.modal.js";
import Responsible from "../models/responsible.modal.js";
import Location from "../models/location.modal.js";
import Placement from "../models/placement.modal.js";

// Load environment variables
dotenv.config();

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Function to import tools from JSON file into MongoDB
 */
async function importFromJson() {
  const startTime = Date.now();
  
  try {
    console.log("🚀 Starting JSON import process...");
    console.log(`⏰ Start time: ${new Date().toISOString()}`);

    // Validate environment
    if (!process.env.MONGO_URI) {
      throw new Error("MONGO_URI environment variable is not set");
    }

    // Connect to MongoDB
    console.log("🔌 Connecting to MongoDB...");
    await mongoose.connect(process.env.MONGO_URI, {
      serverSelectionTimeoutMS: 5000,
    });
    console.log("✅ Connected to MongoDB");

    // Read JSON file
    const jsonPath = path.join(__dirname, "../../frontend/src/data/tools_import.json");
    console.log(`📁 Reading JSON file: ${jsonPath}`);

    let jsonData;
    try {
      const jsonContent = await fs.promises.readFile(jsonPath, 'utf8');
      jsonData = JSON.parse(jsonContent);
      console.log("✅ JSON file read successfully");
    } catch (error) {
      throw new Error(`Failed to read JSON file: ${error.message}`);
    }

    console.log(`📊 Found ${jsonData.tools.length} tools to import`);

    // Import related entities first
    console.log("\n🔄 Creating related entities...");
    
    // Create responsibles
    const responsibleMap = new Map();
    for (const respData of jsonData.relatedEntities.responsibles) {
      try {
        let responsible = await Responsible.findOne({ 
          name: new RegExp(`^${respData.name}$`, "i") 
        });

        if (!responsible) {
          responsible = new Responsible(respData);
          await responsible.save();
          console.log(`✅ Created responsible: ${respData.name}`);
        }
        
        responsibleMap.set(respData.name, responsible._id);
      } catch (error) {
        console.error(`❌ Error creating responsible ${respData.name}:`, error.message);
      }
    }

    // Create locations
    const locationMap = new Map();
    for (const locData of jsonData.relatedEntities.locations) {
      try {
        let location = await Location.findOne({ 
          name: new RegExp(`^${locData.name}$`, "i") 
        });

        if (!location) {
          location = new Location(locData);
          await location.save();
          console.log(`✅ Created location: ${locData.name}`);
        }
        
        locationMap.set(locData.name, location._id);
      } catch (error) {
        console.error(`❌ Error creating location ${locData.name}:`, error.message);
      }
    }

    // Create placements
    const placementMap = new Map();
    for (const placeData of jsonData.relatedEntities.placements) {
      try {
        let placement = await Placement.findOne({ 
          name: new RegExp(`^${placeData.name}$`, "i") 
        });

        if (!placement) {
          placement = new Placement(placeData);
          await placement.save();
          console.log(`✅ Created placement: ${placeData.name}`);
        }
        
        placementMap.set(placeData.name, placement._id);
      } catch (error) {
        console.error(`❌ Error creating placement ${placeData.name}:`, error.message);
      }
    }

    // Import tools
    console.log("\n🔄 Importing tools...");
    const results = {
      success: 0,
      errors: 0,
      skipped: 0,
      details: []
    };

    for (const toolData of jsonData.tools) {
      try {
        // Check if tool already exists
        const existingTool = await Tooling.findOne({
          designation: new RegExp(`^${toolData.designation}$`, "i")
        });

        if (existingTool) {
          console.log(`⚠️ Tool already exists: ${toolData.designation}`);
          results.skipped++;
          continue;
        }

        // Resolve ObjectId references
        const toolDoc = {
          ...toolData,
          responsible: toolData.responsible ? responsibleMap.get(toolData.responsible) : null,
          location: toolData.location ? locationMap.get(toolData.location) : null,
          placement: toolData.placement ? placementMap.get(toolData.placement) : null,
          acquisitionDate: new Date(toolData.acquisitionDate),
          history: toolData.history.map(h => ({
            ...h,
            date: new Date(h.date)
          }))
        };

        // Remove import metadata
        delete toolDoc._importMeta;

        // Create and save tool
        const newTool = new Tooling(toolDoc);
        await newTool.save();

        console.log(`✅ Imported: ${newTool.designation} (MAT: ${newTool.mat})`);
        results.success++;
        results.details.push({
          designation: newTool.designation,
          mat: newTool.mat,
          status: "success"
        });

      } catch (error) {
        console.error(`❌ Error importing ${toolData.designation}:`, error.message);
        results.errors++;
        results.details.push({
          designation: toolData.designation,
          status: "error",
          error: error.message
        });
      }
    }

    // Calculate execution time
    const executionTime = Date.now() - startTime;
    
    // Print summary
    console.log("\n" + "=".repeat(50));
    console.log("📊 IMPORT SUMMARY");
    console.log("=".repeat(50));
    console.log(`✅ Successfully imported: ${results.success} tools`);
    console.log(`⚠️ Skipped (duplicates): ${results.skipped} tools`);
    console.log(`❌ Errors: ${results.errors} tools`);
    console.log(`👥 Responsibles created: ${jsonData.relatedEntities.responsibles.length}`);
    console.log(`📍 Locations created: ${jsonData.relatedEntities.locations.length}`);
    console.log(`🏢 Placements created: ${jsonData.relatedEntities.placements.length}`);
    console.log(`⏱️ Total execution time: ${(executionTime / 1000).toFixed(2)} seconds`);
    console.log(`📅 Completed at: ${new Date().toISOString()}`);

    if (results.errors > 0) {
      console.log("\n❌ ERROR DETAILS:");
      console.log("-".repeat(30));
      results.details
        .filter(d => d.status === "error")
        .forEach(detail => {
          console.log(`   ${detail.designation}: ${detail.error}`);
        });
    }

    console.log("\n🎉 Import process completed successfully!");
    
    return {
      success: true,
      summary: results,
      executionTime: executionTime
    };

  } catch (error) {
    const executionTime = Date.now() - startTime;
    
    console.error("\n" + "=".repeat(50));
    console.error("💥 IMPORT FAILED");
    console.error("=".repeat(50));
    console.error(`❌ Error: ${error.message}`);
    console.error(`⏱️ Failed after: ${(executionTime / 1000).toFixed(2)} seconds`);
    console.error(`📅 Failed at: ${new Date().toISOString()}`);
    
    if (error.stack) {
      console.error("\n🔍 Stack trace:");
      console.error(error.stack);
    }
    
    return {
      success: false,
      error: error.message,
      executionTime: executionTime
    };
    
  } finally {
    // Close MongoDB connection
    try {
      await mongoose.disconnect();
      console.log("🔌 Disconnected from MongoDB");
    } catch (disconnectError) {
      console.error("⚠️ Error disconnecting from MongoDB:", disconnectError.message);
    }
  }
}

// Run the import if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  importFromJson()
    .then((result) => {
      if (result.success) {
        console.log("\n✅ Import completed successfully!");
        process.exit(0);
      } else {
        console.log("\n❌ Import failed!");
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error("\n💥 Unexpected error:", error.message);
      process.exit(1);
    });
}

export default importFromJson;
