{"name": "upkeeppro", "version": "1.0.0", "description": "", "main": "backend/server.js", "scripts": {"dev": "nodemon backend/server.js", "start": "node backend/server.js"}, "keywords": [], "author": "", "type": "module", "license": "ISC", "dependencies": {"@syncfusion/ej2": "^28.2.11", "@syncfusion/ej2-react-charts": "^28.2.11", "@syncfusion/ej2-react-grids": "^28.2.11", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "chart.js": "^4.4.9", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.6", "multer": "^2.0.0", "node-cron": "^4.0.7", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.18.0", "react-calendar": "^5.1.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.1.0", "recharts": "^2.15.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.9"}}