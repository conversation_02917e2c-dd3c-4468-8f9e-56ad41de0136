# UpKeepPro 🏭

![UpKeepPro Banner](https://img.shields.io/badge/UpKeepPro-Maintenance%20Management-blue)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](http://makeapullrequest.com)

A comprehensive maintenance and facility management solution designed to streamline operations, track assets, and optimize maintenance workflows.

![UpKeepPro Dashboard Preview](docs/images/dashboard-preview.png)

## ✨ Features

### 🛠️ Tool Management
- Track tools with unique MAT numbers
- Monitor tool lifecycle from acquisition to retirement
- Record tool movements and usage history
- Manage tool availability and stock levels

### 📝 Work Order System
- Create and assign maintenance tasks
- Track work order status and progress
- Set priorities and due dates
- Attach files and notes to work orders

### 📊 Asset Tracking
- Comprehensive asset registry
- Maintenance history and service records
- Depreciation tracking
- Barcode/QR code support

### 📱 Modern UI/UX
- Clean, intuitive interface
- Dark/Light mode support
- Mobile-responsive design
- Real-time updates

## 🚀 Quick Start

### Prerequisites
- Node.js (v16+)
- MongoDB (v5+)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone [https://github.com/yahya-salhi/UpKeepPro.git](https://github.com/yahya-salhi/UpKeepPro.git)
   cd UpKeepPro

