import mongoose from "mongoose";
import Responsible from "../models/responsible.modal.js";
import Location from "../models/location.modal.js";
import Placement from "../models/placement.modal.js";

/**
 * Helper function to resolve or create a Responsible entity
 * @param {string} responsibleName - The name of the responsible person
 * @returns {Promise<ObjectId|null>} - The ObjectId of the responsible or null
 */
export async function resolveResponsible(responsibleName) {
  if (!responsibleName || responsibleName.trim() === "") {
    return null;
  }

  const name = responsibleName.trim();
  
  try {
    // Try to find existing responsible
    let responsible = await Responsible.findOne({ 
      name: new RegExp(`^${name}$`, "i") 
    });

    if (!responsible) {
      // Create new responsible if not found
      // Extract grade from name if it follows military format (e.g., "Cne Rami <PERSON>")
      const militaryRanks = ["Gal", "Col", "Lcl", "Cdt", "Cne", "Lt", "Slt", "Adj", "Sgt", "Cpl", "<PERSON>"];
      let grade = "Unknown";
      
      for (const rank of militaryRanks) {
        if (name.toLowerCase().startsWith(rank.toLowerCase())) {
          grade = rank;
          break;
        }
      }

      responsible = new Responsible({
        name: name,
        grade: grade
      });
      
      await responsible.save();
      console.log(`✅ Created new responsible: ${name} (${grade})`);
    }

    return responsible._id;
  } catch (error) {
    console.error(`❌ Error resolving responsible "${name}":`, error.message);
    return null;
  }
}

/**
 * Helper function to resolve or create a Location entity
 * @param {string} locationName - The name of the location
 * @returns {Promise<ObjectId|null>} - The ObjectId of the location or null
 */
export async function resolveLocation(locationName) {
  if (!locationName || locationName.trim() === "") {
    return null;
  }

  const name = locationName.trim();
  
  try {
    // Try to find existing location
    let location = await Location.findOne({ 
      name: new RegExp(`^${name}$`, "i") 
    });

    if (!location) {
      // Create new location if not found
      location = new Location({
        name: name,
        description: `Auto-created during Excel import`
      });
      
      await location.save();
      console.log(`✅ Created new location: ${name}`);
    }

    return location._id;
  } catch (error) {
    console.error(`❌ Error resolving location "${name}":`, error.message);
    return null;
  }
}

/**
 * Helper function to resolve or create a Placement entity
 * @param {string} placementName - The name of the placement/emplacement
 * @returns {Promise<ObjectId|null>} - The ObjectId of the placement or null
 */
export async function resolvePlacement(placementName) {
  if (!placementName || placementName.trim() === "") {
    return null;
  }

  const name = placementName.trim();
  
  try {
    // Try to find existing placement
    let placement = await Placement.findOne({ 
      name: new RegExp(`^${name}$`, "i") 
    });

    if (!placement) {
      // Create new placement if not found
      placement = new Placement({
        name: name,
        description: `Auto-created during Excel import`
      });
      
      await placement.save();
      console.log(`✅ Created new placement: ${name}`);
    }

    return placement._id;
  } catch (error) {
    console.error(`❌ Error resolving placement "${name}":`, error.message);
    return null;
  }
}

/**
 * Helper function to map Excel type values to schema enum values
 * @param {string} excelType - The type value from Excel
 * @returns {string} - The mapped type value for the schema
 */
export function mapToolType(excelType) {
  if (!excelType) return "common";
  
  const type = excelType.toLowerCase().trim();
  
  // Mapping from Excel values to schema enum values
  const typeMapping = {
    "com": "common",
    "common": "common",
    "cal": "calibration",
    "calibration": "calibration",
    "main": "maintenance", 
    "maintenance": "maintenance",
    "did": "didactic",
    "didactic": "didactic"
  };

  return typeMapping[type] || "common";
}

/**
 * Helper function to parse date from Excel
 * @param {string|Date} dateValue - The date value from Excel
 * @returns {Date|null} - Parsed date or null
 */
export function parseExcelDate(dateValue) {
  if (!dateValue) return null;
  
  try {
    // If it's already a Date object
    if (dateValue instanceof Date) {
      return dateValue;
    }
    
    // If it's a string, try to parse it
    if (typeof dateValue === "string") {
      // Handle DD/MM/YYYY format
      const dateParts = dateValue.split("/");
      if (dateParts.length === 3) {
        const day = parseInt(dateParts[0]);
        const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
        const year = parseInt(dateParts[2]);
        return new Date(year, month, day);
      }
      
      // Try standard Date parsing
      const parsed = new Date(dateValue);
      if (!isNaN(parsed.getTime())) {
        return parsed;
      }
    }
    
    // If it's a number (Excel serial date)
    if (typeof dateValue === "number") {
      // Excel date serial number (days since 1900-01-01)
      const excelEpoch = new Date(1900, 0, 1);
      const date = new Date(excelEpoch.getTime() + (dateValue - 2) * 24 * 60 * 60 * 1000);
      return date;
    }
    
    return null;
  } catch (error) {
    console.error(`❌ Error parsing date "${dateValue}":`, error.message);
    return null;
  }
}

/**
 * Helper function to validate direction value
 * @param {string} direction - The direction value from Excel
 * @returns {string} - Valid direction value
 */
export function validateDirection(direction) {
  if (!direction) return "DGMRE";
  
  const validDirections = ["DGMRE", "DGTI", "DGGM", "DHS", "DASIC"];
  const dir = direction.toUpperCase().trim();
  
  return validDirections.includes(dir) ? dir : "DGMRE";
}
