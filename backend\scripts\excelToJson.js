import ExcelJS from "exceljs";
import path from "path";
import { fileURLToPath } from "url";
import fs from "fs";
import {
  mapToolType,
  parseExcelDate,
  validateDirection,
} from "./importHelpers.js";

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Function to convert Excel data to JSON format for manual import
 */
async function excelToJson() {
  try {
    console.log("📊 Converting Excel to JSON...");
    console.log("=" .repeat(50));

    // Path to the Excel file
    const excelFilePath = path.join(__dirname, "../../frontend/src/data/liste des équipement.xlsx");
    console.log(`📁 Reading Excel file: ${excelFilePath}`);

    // Validate file exists
    try {
      await fs.promises.access(excelFilePath);
      console.log("✅ Excel file found");
    } catch (error) {
      throw new Error(`Excel file not found: ${excelFilePath}`);
    }

    // Create workbook and read the Excel file
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(excelFilePath);
    console.log("✅ Excel file read successfully");

    // Get the first worksheet
    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      throw new Error("No worksheet found in the Excel file");
    }

    console.log(`📊 Worksheet: ${worksheet.name}`);
    console.log(`📋 Total rows: ${worksheet.rowCount}`);

    // Get header row
    const headerRow = worksheet.getRow(1);
    const headers = [];
    headerRow.eachCell((cell, colNumber) => {
      headers[colNumber] = cell.value?.toString().toLowerCase().trim();
    });

    // Define column mapping
    const columnMapping = {
      designation: findColumnIndex(headers, ["designation", "désignation"]),
      type: findColumnIndex(headers, ["type"]),
      emplacement: findColumnIndex(headers, ["emplacement", "placement"]),
      location: findColumnIndex(headers, ["location", "lieu"]),
      reference: findColumnIndex(headers, ["reference", "référence"]),
      dateReference: findColumnIndex(headers, ["datereference", "date", "date référence"]),
      responsible: findColumnIndex(headers, ["responsible", "responsable"]),
      direction: findColumnIndex(headers, ["direction"]),
    };

    console.log("🗺️ Column mapping found:");
    Object.entries(columnMapping).forEach(([field, columnIndex]) => {
      const status = columnIndex ? "✅" : "❌";
      const header = columnIndex ? headers[columnIndex] : "Not found";
      console.log(`   ${field}: ${status} Column ${columnIndex} (${header})`);
    });

    // Process data rows
    const toolsData = [];
    const responsibles = new Set();
    const locations = new Set();
    const placements = new Set();

    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);
      
      // Extract data from row
      const rowData = extractRowData(row, columnMapping);
      
      // Skip empty rows
      if (!rowData.designation || rowData.designation.trim() === "") {
        continue;
      }

      // Process and clean data
      const designation = rowData.designation.toUpperCase().trim();
      const type = mapToolType(rowData.type);
      const direction = validateDirection(rowData.direction);
      const acquisitionDate = parseExcelDate(rowData.dateReference) || new Date();
      
      // Generate MAT code (first 2 letters + sequential number)
      const prefix = designation.substring(0, 2).toUpperCase();
      const existingWithPrefix = toolsData.filter(tool => tool.mat.startsWith(prefix));
      const matNumber = existingWithPrefix.length + 1;
      const mat = `${prefix}${String(matNumber).padStart(3, "0")}`;

      // Collect unique entities for reference
      if (rowData.responsible) responsibles.add(rowData.responsible.trim());
      if (rowData.location) locations.add(rowData.location.trim());
      if (rowData.emplacement) placements.add(rowData.emplacement.trim());

      // Create tool object matching the Tooling schema
      const toolData = {
        designation: designation,
        mat: mat,
        acquisitionType: "M11", // Default for inventory items
        acquisitionRef: rowData.reference || "inventaire 2025",
        acquisitionDate: acquisitionDate.toISOString(),
        originalQte: 1,
        currentQte: 1,
        responsible: rowData.responsible ? rowData.responsible.trim() : null,
        location: rowData.location ? rowData.location.trim() : null,
        placement: rowData.emplacement ? rowData.emplacement.trim() : null,
        type: type,
        direction: direction,
        situation: "available",
        notes: `Imported from Excel on ${new Date().toISOString()}`,
        history: [
          {
            eventType: "entry",
            reference: `m11-${rowData.reference || "inventaire 2025"}`,
            date: acquisitionDate.toISOString(),
            qteChange: 1,
            notes: "Initial M11 acquisition from Excel import",
            performedBy: "system",
          },
        ],
        exits: [],
        // Metadata for import
        _importMeta: {
          excelRow: rowNumber,
          originalData: rowData
        }
      };

      toolsData.push(toolData);
    }

    // Create output object
    const output = {
      metadata: {
        exportDate: new Date().toISOString(),
        totalTools: toolsData.length,
        excelFile: "liste des équipement.xlsx",
        columnMapping: columnMapping,
        instructions: "This JSON contains tool data ready for manual import into MongoDB"
      },
      relatedEntities: {
        responsibles: Array.from(responsibles).map(name => {
          // Extract grade from name if it follows military format
          const militaryRanks = ["Gal", "Col", "Lcl", "Cdt", "Cne", "Lt", "Slt", "Adj", "Sgt", "Cpl", "Sol"];
          let grade = "Unknown";
          
          for (const rank of militaryRanks) {
            if (name.toLowerCase().startsWith(rank.toLowerCase())) {
              grade = rank;
              break;
            }
          }
          
          return { name, grade };
        }),
        locations: Array.from(locations).map(name => ({
          name,
          description: "Auto-created during Excel import"
        })),
        placements: Array.from(placements).map(name => ({
          name,
          description: "Auto-created during Excel import"
        }))
      },
      tools: toolsData
    };

    // Write to JSON file
    const outputPath = path.join(__dirname, "../../frontend/src/data/tools_import.json");
    await fs.promises.writeFile(outputPath, JSON.stringify(output, null, 2), 'utf8');

    console.log("\n📊 Conversion Summary:");
    console.log(`✅ Processed ${toolsData.length} tools`);
    console.log(`👥 Found ${responsibles.size} unique responsibles`);
    console.log(`📍 Found ${locations.size} unique locations`);
    console.log(`🏢 Found ${placements.size} unique placements`);
    console.log(`💾 JSON file saved to: ${outputPath}`);

    console.log("\n🎉 Excel to JSON conversion completed!");
    
    return {
      success: true,
      outputPath: outputPath,
      summary: {
        tools: toolsData.length,
        responsibles: responsibles.size,
        locations: locations.size,
        placements: placements.size
      }
    };

  } catch (error) {
    console.error("\n💥 Conversion failed:", error.message);
    if (error.stack) {
      console.error("\n🔍 Stack trace:");
      console.error(error.stack);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Helper function to find column index by header names
 */
function findColumnIndex(headers, possibleNames) {
  for (const name of possibleNames) {
    const index = headers.findIndex(header => 
      header && header.includes(name.toLowerCase())
    );
    if (index !== -1) return index;
  }
  return null;
}

/**
 * Helper function to extract data from a row
 */
function extractRowData(row, columnMapping) {
  return {
    designation: getCellValue(row, columnMapping.designation),
    type: getCellValue(row, columnMapping.type),
    emplacement: getCellValue(row, columnMapping.emplacement),
    location: getCellValue(row, columnMapping.location),
    reference: getCellValue(row, columnMapping.reference),
    dateReference: getCellValue(row, columnMapping.dateReference),
    responsible: getCellValue(row, columnMapping.responsible),
    direction: getCellValue(row, columnMapping.direction),
  };
}

/**
 * Helper function to get cell value safely
 */
function getCellValue(row, columnIndex) {
  if (!columnIndex) return null;
  const cell = row.getCell(columnIndex);
  return cell.value?.toString().trim() || null;
}

// Run the conversion if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  excelToJson()
    .then((result) => {
      if (result.success) {
        console.log("\n✅ Conversion completed successfully!");
        console.log(`📁 Output file: ${result.outputPath}`);
        process.exit(0);
      } else {
        console.log("\n❌ Conversion failed!");
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error("\n💥 Unexpected error:", error.message);
      process.exit(1);
    });
}

export default excelToJson;
