import mongoose from "mongoose";
import ExcelJS from "exceljs";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// Import models
import Tooling from "../models/tooling.modal.js";
import {
  resolveResponsible,
  resolveLocation,
  resolvePlacement,
  mapToolType,
  parseExcelDate,
  validateDirection,
} from "./importHelpers.js";

// Load environment variables
dotenv.config();

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Main function to import Excel data into Tooling collection
 */
async function importExcelData() {
  const startTime = Date.now();

  try {
    console.log("🚀 Starting Excel import process...");
    console.log(`⏰ Start time: ${new Date().toISOString()}`);

    // Validate environment
    if (!process.env.MONGO_URI) {
      throw new Error("MONGO_URI environment variable is not set");
    }

    // Connect to MongoDB with error handling
    console.log("🔌 Connecting to MongoDB...");
    await mongoose.connect(process.env.MONGO_URI, {
      serverSelectionTimeoutMS: 5000, // 5 second timeout
    });
    console.log("✅ Connected to MongoDB");

    // Path to the Excel file
    const excelFilePath = path.join(
      __dirname,
      "../../frontend/src/data/liste des équipement.xlsx"
    );
    console.log(`📁 Reading Excel file: ${excelFilePath}`);

    // Validate file exists
    try {
      await import("fs").then((fs) => fs.promises.access(excelFilePath));
    } catch (error) {
      throw new Error(`Excel file not found: ${excelFilePath}`);
    }

    // Create workbook and read the Excel file
    const workbook = new ExcelJS.Workbook();
    try {
      await workbook.xlsx.readFile(excelFilePath);
    } catch (error) {
      throw new Error(`Failed to read Excel file: ${error.message}`);
    }

    // Get the first worksheet
    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      throw new Error("No worksheet found in the Excel file");
    }

    console.log(`📊 Found worksheet: ${worksheet.name}`);
    console.log(`📋 Total rows: ${worksheet.rowCount}`);

    // Get header row to understand column structure
    const headerRow = worksheet.getRow(1);
    const headers = [];
    headerRow.eachCell((cell, colNumber) => {
      headers[colNumber] = cell.value?.toString().toLowerCase().trim();
    });

    console.log(
      "📝 Headers found:",
      headers.filter((h) => h)
    );

    // Define column mapping based on expected headers
    const columnMapping = {
      designation: findColumnIndex(headers, ["designation", "désignation"]),
      type: findColumnIndex(headers, ["type"]),
      emplacement: findColumnIndex(headers, ["emplacement", "placement"]),
      location: findColumnIndex(headers, ["location", "lieu"]),
      reference: findColumnIndex(headers, ["reference", "référence"]),
      dateReference: findColumnIndex(headers, [
        "datereference",
        "date",
        "date référence",
      ]),
      responsible: findColumnIndex(headers, ["responsible", "responsable"]),
      direction: findColumnIndex(headers, ["direction"]),
    };

    console.log("🗺️ Column mapping:", columnMapping);

    // Validate that required columns are found
    const requiredColumns = ["designation", "type", "direction"];
    for (const col of requiredColumns) {
      if (!columnMapping[col]) {
        throw new Error(`Required column '${col}' not found in Excel file`);
      }
    }

    // Process each data row (skip header row)
    const results = {
      success: 0,
      errors: 0,
      skipped: 0,
      details: [],
    };

    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      try {
        // Extract data from row
        const rowData = extractRowData(row, columnMapping);

        // Skip empty rows
        if (!rowData.designation || rowData.designation.trim() === "") {
          results.skipped++;
          continue;
        }

        console.log(`\n🔄 Processing row ${rowNumber}: ${rowData.designation}`);

        // Check if tool already exists
        const existingTool = await Tooling.findOne({
          designation: new RegExp(`^${rowData.designation}$`, "i"),
        });

        if (existingTool) {
          console.log(`⚠️ Tool already exists: ${rowData.designation}`);
          results.skipped++;
          continue;
        }

        // Resolve related entities
        const responsibleId = await resolveResponsible(rowData.responsible);
        const locationId = await resolveLocation(rowData.location);
        const placementId = await resolvePlacement(rowData.emplacement);

        // Parse and validate data
        const acquisitionDate =
          parseExcelDate(rowData.dateReference) || new Date();
        const toolType = mapToolType(rowData.type);
        const direction = validateDirection(rowData.direction);

        // Create new tooling entry
        const newTool = new Tooling({
          designation: rowData.designation.toUpperCase().trim(),
          acquisitionType: "M11", // Default to M11 for inventory items
          acquisitionRef: rowData.reference || "inventaire 2025",
          acquisitionDate: acquisitionDate,
          originalQte: 1, // Default quantity
          currentQte: 1,
          responsible: responsibleId,
          location: locationId,
          placement: placementId,
          type: toolType,
          direction: direction,
          situation: "available",
          notes: `Imported from Excel on ${new Date().toISOString()}`,
          history: [
            {
              eventType: "entry",
              reference: `m11-${rowData.reference || "inventaire 2025"}`,
              date: acquisitionDate,
              qteChange: 1,
              notes: "Initial M11 acquisition from Excel import",
              performedBy: "system",
            },
          ],
        });

        // Save the tool (MAT will be auto-generated by the schema pre-save hook)
        await newTool.save();

        console.log(
          `✅ Successfully imported: ${newTool.designation} (MAT: ${newTool.mat})`
        );
        results.success++;
        results.details.push({
          row: rowNumber,
          designation: newTool.designation,
          mat: newTool.mat,
          status: "success",
        });
      } catch (error) {
        console.error(`❌ Error processing row ${rowNumber}:`, error.message);
        results.errors++;
        results.details.push({
          row: rowNumber,
          designation:
            row.getCell(columnMapping.designation).value?.toString() ||
            "Unknown",
          status: "error",
          error: error.message,
        });
      }
    }

    // Calculate execution time
    const executionTime = Date.now() - startTime;

    // Print summary
    console.log("\n" + "=".repeat(50));
    console.log("📊 IMPORT SUMMARY");
    console.log("=".repeat(50));
    console.log(`✅ Successfully imported: ${results.success} tools`);
    console.log(`⚠️ Skipped (duplicates): ${results.skipped} tools`);
    console.log(`❌ Errors: ${results.errors} tools`);
    console.log(
      `⏱️ Total execution time: ${(executionTime / 1000).toFixed(2)} seconds`
    );
    console.log(`📅 Completed at: ${new Date().toISOString()}`);

    if (results.errors > 0) {
      console.log("\n❌ ERROR DETAILS:");
      console.log("-".repeat(30));
      results.details
        .filter((d) => d.status === "error")
        .forEach((detail) => {
          console.log(`   Row ${detail.row}: ${detail.designation}`);
          console.log(`   Error: ${detail.error}\n`);
        });
    }

    if (results.success > 0) {
      console.log("\n✅ SUCCESSFULLY IMPORTED TOOLS:");
      console.log("-".repeat(40));
      results.details
        .filter((d) => d.status === "success")
        .slice(0, 10) // Show first 10 for brevity
        .forEach((detail) => {
          console.log(`   ${detail.designation} (MAT: ${detail.mat})`);
        });

      if (results.success > 10) {
        console.log(`   ... and ${results.success - 10} more tools`);
      }
    }

    console.log("\n🎉 Import process completed successfully!");

    // Return results for potential API usage
    return {
      success: true,
      summary: results,
      executionTime: executionTime,
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;

    console.error("\n" + "=".repeat(50));
    console.error("💥 IMPORT FAILED");
    console.error("=".repeat(50));
    console.error(`❌ Error: ${error.message}`);
    console.error(
      `⏱️ Failed after: ${(executionTime / 1000).toFixed(2)} seconds`
    );
    console.error(`📅 Failed at: ${new Date().toISOString()}`);

    if (error.stack) {
      console.error("\n🔍 Stack trace:");
      console.error(error.stack);
    }

    // Return error result
    return {
      success: false,
      error: error.message,
      executionTime: executionTime,
    };
  } finally {
    // Close MongoDB connection
    try {
      await mongoose.disconnect();
      console.log("🔌 Disconnected from MongoDB");
    } catch (disconnectError) {
      console.error(
        "⚠️ Error disconnecting from MongoDB:",
        disconnectError.message
      );
    }
  }
}

/**
 * Helper function to find column index by header names
 */
function findColumnIndex(headers, possibleNames) {
  for (const name of possibleNames) {
    const index = headers.findIndex(
      (header) => header && header.includes(name.toLowerCase())
    );
    if (index !== -1) return index;
  }
  return null;
}

/**
 * Helper function to extract data from a row
 */
function extractRowData(row, columnMapping) {
  return {
    designation: getCellValue(row, columnMapping.designation),
    type: getCellValue(row, columnMapping.type),
    emplacement: getCellValue(row, columnMapping.emplacement),
    location: getCellValue(row, columnMapping.location),
    reference: getCellValue(row, columnMapping.reference),
    dateReference: getCellValue(row, columnMapping.dateReference),
    responsible: getCellValue(row, columnMapping.responsible),
    direction: getCellValue(row, columnMapping.direction),
  };
}

/**
 * Helper function to get cell value safely
 */
function getCellValue(row, columnIndex) {
  if (!columnIndex) return null;
  const cell = row.getCell(columnIndex);
  return cell.value?.toString().trim() || null;
}

// Run the import if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  importExcelData();
}

export default importExcelData;
