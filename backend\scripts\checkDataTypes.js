import mongoose from "mongoose";
import dotenv from "dotenv";

// Import models
import Tooling from "../models/tooling.modal.js";
import Responsible from "../models/responsible.modal.js";
import Location from "../models/location.modal.js";
import Placement from "../models/placement.modal.js";

// Load environment variables
dotenv.config();

/**
 * Check the current data types in the database
 */
async function checkDataTypes() {
  try {
    console.log("🔍 Checking current data types in database...");

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log("✅ Connected to MongoDB");

    // Check tools collection
    const totalTools = await Tooling.countDocuments();
    console.log(`📊 Total tools in database: ${totalTools}`);

    if (totalTools === 0) {
      console.log("📭 No tools found in database. You can import from JSON.");
      return;
    }

    // Check data types
    const stringResponsibles = await <PERSON><PERSON>.countDocuments({ responsible: { $type: "string" } });
    const objectIdResponsibles = await Tooling.countDocuments({ responsible: { $type: "objectId" } });
    const nullResponsibles = await Tooling.countDocuments({ responsible: null });

    const stringLocations = await Tooling.countDocuments({ location: { $type: "string" } });
    const objectIdLocations = await Tooling.countDocuments({ location: { $type: "objectId" } });
    const nullLocations = await Tooling.countDocuments({ location: null });

    const stringPlacements = await Tooling.countDocuments({ placement: { $type: "string" } });
    const objectIdPlacements = await Tooling.countDocuments({ placement: { $type: "objectId" } });
    const nullPlacements = await Tooling.countDocuments({ placement: null });

    // Check reference collections
    const totalResponsibles = await Responsible.countDocuments();
    const totalLocations = await Location.countDocuments();
    const totalPlacements = await Placement.countDocuments();

    console.log("\n" + "=".repeat(60));
    console.log("📊 DATA TYPE ANALYSIS");
    console.log("=".repeat(60));
    
    console.log("\n👥 RESPONSIBLE FIELD:");
    console.log(`   String values: ${stringResponsibles}`);
    console.log(`   ObjectId values: ${objectIdResponsibles}`);
    console.log(`   Null values: ${nullResponsibles}`);
    
    console.log("\n📍 LOCATION FIELD:");
    console.log(`   String values: ${stringLocations}`);
    console.log(`   ObjectId values: ${objectIdLocations}`);
    console.log(`   Null values: ${nullLocations}`);
    
    console.log("\n🏢 PLACEMENT FIELD:");
    console.log(`   String values: ${stringPlacements}`);
    console.log(`   ObjectId values: ${objectIdPlacements}`);
    console.log(`   Null values: ${nullPlacements}`);

    console.log("\n📋 REFERENCE COLLECTIONS:");
    console.log(`   Responsible documents: ${totalResponsibles}`);
    console.log(`   Location documents: ${totalLocations}`);
    console.log(`   Placement documents: ${totalPlacements}`);

    // Sample data
    console.log("\n🔍 SAMPLE DATA:");
    const sampleTool = await Tooling.findOne().limit(1);
    if (sampleTool) {
      console.log(`   Sample tool: ${sampleTool.designation}`);
      console.log(`   Responsible: ${sampleTool.responsible} (${typeof sampleTool.responsible})`);
      console.log(`   Location: ${sampleTool.location} (${typeof sampleTool.location})`);
      console.log(`   Placement: ${sampleTool.placement} (${typeof sampleTool.placement})`);
    }

    // Recommendations
    console.log("\n💡 RECOMMENDATIONS:");
    if (stringResponsibles > 0 || stringLocations > 0 || stringPlacements > 0) {
      console.log("   ⚠️  You have string values that should be converted to ObjectIds");
      console.log("   🔧 Run: node scripts/convertStringToObjectId.js");
    } else if (objectIdResponsibles > 0 || objectIdLocations > 0 || objectIdPlacements > 0) {
      console.log("   ✅ Your data is already using ObjectId references");
    } else {
      console.log("   📭 No reference data found. Import from JSON first.");
    }

  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("\n🔌 Disconnected from MongoDB");
  }
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  checkDataTypes();
}

export default checkDataTypes;
